import { sub, format } from 'date-fns'
import { mkConfig, generateCsv } from 'export-to-csv'
import { saveAs } from 'file-saver'

const DEFAULT_LOGS_FILTER = {
  range: { start: sub(new Date(), { days: 3 }), end: new Date() }
}

export const useLogsStore = defineStore('logsStore', {
  state: () => ({
    logs: [] as any[],
    logsFilter: DEFAULT_LOGS_FILTER,
    logsSubFilter: {} as Record<string, any>,
    logsFilterKeyword: '',
    selectedLog: null,
    loadings: {} as Record<string, any>,
    errors: {} as Record<string, any>,
    logPagination: {
      page: 1,
      pageCount: 10,
      asc: false
    },
    logPaginationTotal: 0
  }),
  getters: {
    logsFilterRange: state => ({
      from_date: format(state.logsFilter.range.start, 'yyyy-MM-dd'),
      to_date: format(state.logsFilter.range.end, 'yyyy-MM-dd')
    }),
    logsFiltered: (state) => {
      return state.logs.filter((log) => {
        return (
          log?.answer?.includes(state.logsFilterKeyword)
          || log?.query?.includes(state.logsFilterKeyword)
          || log?.query_jp?.includes(state.logsFilterKeyword)
          || log?.answer_jp?.includes(state.logsFilterKeyword)
          || !state.logsFilterKeyword
        )
      })
    },
    logsFilteredUniqueBySessionId(): any[] {
      const uniqueLogs = []
      const uniqueSessionIds = new Set()
      for (const log of this.logsFiltered) {
        if (!uniqueSessionIds.has(log.session_id)) {
          uniqueLogs.push(log)
          uniqueSessionIds.add(log.session_id)
        }
      }
      return uniqueLogs
    },
    allRelatedLogs(): any[] {
      return this.logsFiltered
        .filter(log => log.session_id === this.selectedLog?.session_id)
        .sort(
          (a, b) =>
            new Date(a.query_created_at).getTime()
              - new Date(b.query_created_at).getTime()
        )
    },
    isShowTokenInfo(): boolean {
      const authStore = useAuthStore()
      return authStore.isOperator
    }
  },
  actions: {
    async searchLogs(tenant_id: string, _environment: number) {
      try {
        this.loadings.searchLogs = true
        this.errors.searchLogs = null
        this.logs = []
        this.logPaginationTotal = 0
        // Use role-based API calls
        const roleBasedApiCalls = useRoleBasedApiCalls()

        // Define the endpoints for different roles
        const response = await roleBasedApiCalls.custom(
          {
            operator: '/v2/histories/all/pagination/tenants/{tenantId}', // Operator can see logs from all tenants
            admin: '/v2/histories/all/pagination', // Admin can only see logs from their tenant
            staff: '/v2/histories/all/pagination', // Staff can only see logs from their tenant
            default: '/v2/histories/all/pagination/tenants/{tenantId}',
            params: {
              tenantId: tenant_id
            },
            query: {
              ...this.logsFilterRange,
              page: this.logPagination.page,
              page_size: this.logPagination.pageCount,
              order: this.logPagination.asc,
              environment: _environment,
              query: this.logsSubFilter?.query || null,
              answer: this.logsSubFilter?.answer || null,
              request_id: this.logsSubFilter?.request_id || null,
              session_id: this.logsSubFilter?.session_id || null,
              category_id: this.logsSubFilter?.category_id?.value ?? null,
              context_type: this.logsSubFilter?.context_type?.value ?? null,
              analyzed_action:
                this.logsSubFilter?.analyzed_action?.value ?? null,
              processed: this.logsSubFilter?.processed?.value ?? null
            }
          },
          'get'
        )

        this.logs = response?.histories || []
        const pagination = tryParseJson(response?.pagination)
        this.logPaginationTotal = pagination?.total_count
        return true
      } catch (error: any) {
        this.errors.searchLogs = error?.response?.data || error
        return false
      } finally {
        this.loadings.searchLogs = false
      }
    },

    async exportAllLogs(tenant_id: string, _environment: number) {
      try {
        this.loadings.exportAllLogs = true
        this.errors.exportAllLogs = null

        // Use role-based API calls
        const roleBasedApiCalls = useRoleBasedApiCalls()

        // Define the endpoints for different roles with page_size 10000
        const response = await roleBasedApiCalls.custom(
          {
            operator: '/v2/histories/all/pagination/tenants/{tenantId}', // Operator can see logs from all tenants
            admin: '/v2/histories/all/pagination', // Admin can only see logs from their tenant
            staff: '/v2/histories/all/pagination', // Staff can only see logs from their tenant
            default: '/v2/histories/all/pagination/tenants/{tenantId}',
            params: {
              tenantId: tenant_id
            },
            query: {
              ...this.logsFilterRange,
              page: 1, // Always start from page 1 for export
              page_size: 10000, // Large page size to get all logs
              order: this.logPagination.asc,
              environment: _environment,
              query: this.logsSubFilter?.query || null,
              answer: this.logsSubFilter?.answer || null,
              request_id: this.logsSubFilter?.request_id || null,
              session_id: this.logsSubFilter?.session_id || null,
              category_id: this.logsSubFilter?.category_id?.value ?? null,
              context_type: this.logsSubFilter?.context_type?.value ?? null,
              analyzed_action:
                this.logsSubFilter?.analyzed_action?.value ?? null,
              processed: this.logsSubFilter?.processed?.value ?? null
            }
          },
          'get'
        )

        const allLogs = response?.histories || []

        // Export the logs to CSV
        this.exportLogsToCSV(allLogs, 'all_logs')

        return true
      } catch (error: any) {
        this.errors.exportAllLogs = error?.response?.data || error
        return false
      } finally {
        this.loadings.exportAllLogs = false
      }
    },
    exportLogsToCSV(rows: any[], documentName: string, categories?: any[]) {
      const categoryMap = categories
        ? new Map(
          categories.map(category => [category.value, category.label])
        )
        : null

      rows = rows.map(row => ({
        ...row,
        label: row?.label?.join(';')
      }))

      rows = rows.map((row) => {
        const updatedCategoryId
          = categoryMap?.get(row.category_id) || row.category_id
        return {
          ...row,
          category_id: updatedCategoryId,
          label: row?.label?.join(';')
        }
      })
      const config = mkConfig({ useKeysAsHeaders: true })
      const csvOutput = generateCsv(config)(rows)
      const blob = new Blob([String(csvOutput)], {
        type: 'text/csv;charset=utf-8'
      })
      saveAs(blob, `${documentName || 'logs'}.csv`)
    }
  }
})
