<script setup lang="ts">
import { sub } from 'date-fns'

const reportsStore = useReportsStore()
const ragsStore = useRagsStore()
const toast = useToast()

const {
  range,
  unansweredReportsData,
  unansweredReportFilter,
  selectedCategories,
  loadings,
  unansweredReportsPagination,
  unansweredReportsDataTotal,
  hasAnyUnansweredReportFilter
} = storeToRefs(reportsStore)

onMounted(async () => {
  await ragsStore.ragTenantLogin()
  reportsStore.getUnansweredReport()
})

const refresh = async () => {
  await reportsStore.getUnansweredReport()
}
watch(
  () => range.value,
  async () => {
    refresh()
    selectedCategories.value = []
  }
)
watch(
  () => unansweredReportsPagination.value.pageCount,
  () => {
    unansweredReportsPagination.value.page = 1
  }
)

watch(
  () => unansweredReportsPagination.value,
  () => {
    refresh()
  },
  { deep: true }
)

// Watch for sort changes and update pagination asc property
const sort = ref({ column: 'query_date', direction: 'desc' as 'asc' | 'desc' })
watch(
  () => sort.value,
  (newSort) => {
    // Update the asc property based on sort direction
    unansweredReportsPagination.value.asc = newSort.direction === 'asc'
    // Reset to page 1 when sorting changes
    unansweredReportsPagination.value.page = 1
  },
  { deep: true }
)
const defaultColumns = [
  {
    key: 'ids',
    label: 'ID',
    sortable: false
  },
  {
    key: 'analyzed_action',
    label: 'パターン',
    sortable: false
  },
  {
    key: 'context_type',
    label: 'コンテキスト',
    sortable: false
  },
  {
    key: 'processed',
    label: '処理済み',
    sortable: false
  },
  {
    key: 'query',
    label: '質問',
    sortable: false
  },
  {
    key: 'query_date',
    label: '質問日時',
    sortable: true
  }
]
const items = [
  [
    {
      label: 'CSV出力',
      icon: 'ph:file-csv-light',
      click: () =>
        exportUnansweredQuestionsToCSV(
          selectedCategories.value,
          'unanswered-question-logs'
        )
    }
  ]
]

const exportUnansweredQuestionsToCSV = (rows: any[], documentName?: string) => {
  reportsStore.exportUnansweredQuestionsToCSV(rows, documentName)
}

const rowMenus = (row: any) => {
  return [
    [
      {
        label: 'CSV出力 ',
        icon: 'ph:file-csv-light',
        click: () => exportUnansweredQuestionsToCSV([row], row?.query)
      }
    ]
  ]
}

const showAdvancedSearch = ref(false)

const onAdvancedSearch = (conditions: any) => {
  console.log('🚀 ~ onAdvancedSearch ~ conditions:', conditions)
  unansweredReportFilter.value = conditions
  range.value = conditions.range
  showAdvancedSearch.value = false
  refresh()
}

const onClearAdvancedSearch = () => {
  unansweredReportFilter.value = {
    range: {
      start: sub(new Date(), { days: 14 }),
      end: new Date()
    },
    session_id: '',
    chat_id: '',
    context_type: '',
    analyzed_action: '',
    processed: ''
  }
  refresh()
}

// Function to copy ID to clipboard
const copyToClipboard = (text: string, type: string) => {
  navigator.clipboard.writeText(text)
    .then(() => {
      toast.add({
        title: `${type}をコピーしました`,
        icon: 'i-heroicons-check-circle',
        color: 'green',
        timeout: 2000
      })
    })
    .catch(() => {
      toast.add({
        title: 'コピーに失敗しました',
        icon: 'i-heroicons-exclamation-circle',
        color: 'red',
        timeout: 2000
      })
    })
}
</script>

<template>
  <UDashboardPage>
    <UDashboardPanel grow>
      <UDashboardNavbar
        title="未回答の質問"
        :badge="unansweredReportsDataTotal"
      />

      <UDashboardToolbar>
        <template #left>
          <div class="flex gap-5">
            <BaseDateRangePicker
              v-model="range"
              :exclude-today="true"
            />
          </div>
        </template>
        <template #right>
          <div class="flex items-center gap-1.5">
            <div />
            <div v-if="selectedCategories.length">
              <UDropdown
                :items="items"
                :popper="{ placement: 'bottom-start' }"
              >
                <UButton
                  color="white"
                  :label="`一括操作（${selectedCategories.length}件）`"
                  icon="fluent:form-multiple-20-regular"
                  trailing-icon="i-heroicons-chevron-down-20-solid"
                  size="sm"
                />
              </UDropdown>
            </div>
            <div class="flex gap-4">
              <UButton
                v-if="hasAnyUnansweredReportFilter"
                icon="ant-design:clear-outlined"
                color="gray"
                size="sm"
                label="検索条件をクリア"
                @click="onClearAdvancedSearch"
              />
              <UChip
                size="lg"
                :show="hasAnyUnansweredReportFilter"
              >
                <UButton
                  :icon="'fluent:slide-search-16-regular'"
                  :color="hasAnyUnansweredReportFilter ? 'primary' : 'gray'"
                  size="sm"
                  label="高度な検索"
                  :variant="hasAnyUnansweredReportFilter ? 'outline' : 'solid'"
                  @click="showAdvancedSearch = !showAdvancedSearch"
                />
              </UChip>

              <UButton
                icon="prime:sync"
                color="gray"
                size="sm"
                @click="refresh"
              />
            </div>
          </div>
        </template>
      </UDashboardToolbar>
      <UTable
        v-if="unansweredReportsData"
        v-model="selectedCategories"
        v-model:sort="sort"
        :rows="unansweredReportsData"
        :columns="defaultColumns"
        :loading="loadings.getUnansweredReport"
        :sort-asc-icon="undefined"
        :sort-desc-icon="undefined"
        class="w-full"
        :ui="{
          divide: 'divide-gray-200 dark:divide-gray-800',
          tr: { base: 'group' }
        }"
      >
        <template #ids-data="{ row }">
          <div class="space-y-2">
            <!-- Session ID -->
            <div class="group flex items-center gap-2 px-2 rounded hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
              <div class="flex-1 min-w-0">
                <div class="text-[10px] text-gray-500 dark:text-gray-400">
                  セッション
                </div>
                <div class="text-xs font-mono truncate">
                  {{ row.session_id }}
                </div>
              </div>
              <UButton
                icon="i-heroicons-clipboard-document"
                color="gray"
                variant="ghost"
                size="xs"
                class="opacity-0 group-hover:opacity-100 transition-opacity"
                @click="copyToClipboard(row.session_id, 'セッションID')"
              />
            </div>
            <!-- Chat ID -->
            <div class="group flex items-center gap-2 px-2 rounded hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
              <div class="flex-1 min-w-0">
                <div class="text-[10px] text-gray-500 dark:text-gray-400">
                  チャット
                </div>
                <div class="text-xs font-mono truncate">
                  {{ row.chat_id }}
                </div>
              </div>
              <UButton
                icon="i-heroicons-clipboard-document"
                color="gray"
                variant="ghost"
                size="xs"
                class="opacity-0 group-hover:opacity-100 transition-opacity"
                @click="copyToClipboard(row.chat_id, 'チャットID')"
              />
            </div>
          </div>
        </template>
        <template #query_date-data="{ row }">
          <div class="flex items-center gap-3 justify-between">
            {{ formatDateTime(row.query_date) }}
            <UDropdown
              class="invisible group-hover:visible"
              :items="rowMenus(row)"
              :popper="{ placement: 'bottom-start' }"
            >
              <UButton
                class="row-menu"
                color="white"
                icon="charm:menu-meatball"
                size="xs"
                square
                :loading="false"
              />
            </UDropdown>
          </div>
        </template>
        <template #analyzed_action-data="{ row }">
          <div class="w-24">
            <UBadge
              size="sm"
              variant="subtle"
              :trailing="false"
              v-bind="reportRagObject(row.analyzed_action)"
            />
          </div>
        </template>
        <template #context_type-data="{ row }">
          <div class="w-40">
            <UBadge
              size="sm"
              variant="subtle"
              :trailing="false"
              v-bind="reportContextObject(row.context_type)"
            />
          </div>
        </template>
        <template #processed-data="{ row }">
          <div class="w-40">
            <UBadge
              size="sm"
              variant="subtle"
              :trailing="false"
              v-bind="reportProcessedObject(row.processed)"
            />
          </div>
        </template>
      </UTable>
      <UDivider class="mt-0" />
      <UDashboardToolbar>
        <template #left>
          <div class="flex items-center gap-1.5">
            <span class="text-sm leading-5">表示件数:</span>

            <USelect
              v-model="unansweredReportsPagination.pageCount"
              :options="[3, 5, 10, 20, 30, 40]"
              class="w-20"
            />
          </div>
        </template>

        <template #right>
          <UPagination
            v-if="
              unansweredReportsPagination.pageCount < unansweredReportsDataTotal
            "
            v-model="unansweredReportsPagination.page"
            :page-count="unansweredReportsPagination.pageCount"
            :total="unansweredReportsDataTotal"
            size="sm"
            :ui="{
              wrapper: 'flex items-center gap-1',
              rounded: '!rounded-full min-w-[32px] justify-center',
              default: {
                activeButton: {
                  variant: 'outline'
                }
              }
            }"
          />
        </template>
      </UDashboardToolbar>
    </UDashboardPanel>
    <BaseAdvancedSearch
      :show="showAdvancedSearch"
      :conditions="{
        range,
        ...unansweredReportFilter
      }"
      @close="showAdvancedSearch = false"
      @search="onAdvancedSearch"
    />
  </UDashboardPage>
</template>
