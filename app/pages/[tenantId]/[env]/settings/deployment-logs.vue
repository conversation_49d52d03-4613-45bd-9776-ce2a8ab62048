<script setup lang="ts">
const { selectedTenantId } = useApp()

const deploymentsStore = useDeploymentsStore()
const {
  deploymentLogs,
  range,
  loadings,
  deploymentHistories,
  swapHistories,
  deploymentHistoryPagination,
  swapHistoryPagination,
  deploymentHistoryPageTotal,
  swapHistoryPageTotal,
  deploymentHistoryTotalCount,
  swapHistoryTotalCount,
  deploymentLogPagination,
  deployLogFilter,
  deployLogTotal,
  selectedHistoryTab
} = storeToRefs(deploymentsStore)

// Tab configuration - スワップ履歴 first
const tabItems = [
  { label: 'スワップ履歴', value: 'swap', icon: 'ri:swap-2-fill' },
  {
    label: '同期/ロールバック履歴',
    value: 'sync',
    icon: 'fluent:arrow-sync-circle-20-regular'
  }
]

// URL and tab management
const route = useRoute()
const router = useRouter()

// Initialize tab from URL query parameter
const initializeTabFromUrl = () => {
  const tabParam = route.query.tab as string
  if (tabParam === 'sync') {
    selectedHistoryTab.value = 1 // sync history
  } else {
    selectedHistoryTab.value = 0 // swap history (default)
  }
}

// Update URL when tab changes
const updateUrlTab = (tabIndex: number) => {
  const tabValue = tabIndex === 1 ? 'sync' : 'swap'
  router.replace({
    query: {
      ...route.query,
      tab: tabValue
    }
  })
}

const refresh = async () => {
  if (selectedHistoryTab.value === 1) {
    // sync history
    await deploymentsStore.fetchDeploymentHistories(selectedTenantId.value)
  } else {
    // swap history
    await deploymentsStore.fetchSwapHistories(selectedTenantId.value)
  }
}

// Watch for tab changes to fetch appropriate data and update URL
watch(selectedHistoryTab, async (newTab) => {
  updateUrlTab(newTab)
  if (newTab === 1) {
    // sync history
    await deploymentsStore.fetchDeploymentHistories(selectedTenantId.value)
  } else {
    // swap history
    await deploymentsStore.fetchSwapHistories(selectedTenantId.value)
  }
})

watch(
  () => [deploymentHistoryPagination.value.pageCount, range.value],
  () => {
    deploymentHistoryPagination.value.page = 1
  }
)

watch(
  () => [swapHistoryPagination.value.pageCount, range.value],
  () => {
    swapHistoryPagination.value.page = 1
  }
)

watch(
  () => [deploymentHistoryPagination.value, range.value],
  () => {
    if (selectedHistoryTab.value === 1) {
      // sync history
      refresh()
    }
  },
  { deep: true, immediate: true }
)

watch(
  () => [swapHistoryPagination.value, range.value],
  () => {
    if (selectedHistoryTab.value === 0) {
      // swap history
      refresh()
    }
  },
  { deep: true }
)
const selectedLog = ref<any>(null)
const fetchDeploymentLogs = async () => {
  if (selectedLog.value) {
    return await deploymentsStore.getDeploymentLogs(
      selectedTenantId.value,
      selectedLog.value.id
    )
  }
  return false
}

const isLogsModalOpen = ref(false)

const onViewLogs = async (data: any) => {
  selectedLog.value = data
  isLogsModalOpen.value = true
  await deploymentsStore.getDeploymentLogs(selectedTenantId.value, data.id)
}

// Computed properties for current tab data
const currentTotalCount = computed(() => {
  return selectedHistoryTab.value === 1 // sync history
    ? deploymentHistoryTotalCount.value
    : swapHistoryTotalCount.value
})

const currentPagination = computed(() => {
  return selectedHistoryTab.value === 1 // sync history
    ? deploymentHistoryPagination.value
    : swapHistoryPagination.value
})

const currentPageTotal = computed(() => {
  return selectedHistoryTab.value === 1 // sync history
    ? deploymentHistoryPageTotal.value
    : swapHistoryPageTotal.value
})

// Load initial data based on selected tab
onMounted(async () => {
  // Initialize tab from URL first
  initializeTabFromUrl()

  // Then load appropriate data
  if (selectedHistoryTab.value === 1) {
    // sync history
    await deploymentsStore.fetchDeploymentHistories(selectedTenantId.value)
  } else {
    // swap history
    await deploymentsStore.fetchSwapHistories(selectedTenantId.value)
  }
})
</script>

<template>
  <UDashboardPanelContent class="p-0">
    <UCard
      class="w-full"
      :ui="{
        base: '',
        ring: '',
        divide: 'divide-y divide-gray-200 dark:divide-gray-700',
        header: { padding: '!px-3 !py-3' },
        body: {
          padding: '',
          base: 'divide-y divide-gray-200 dark:divide-gray-700'
        },
        footer: { padding: 'p-4' }
      }"
    >
      <template #header>
        <div class="flex items-center justify-between">
          <h2
            class="flex items-center gap-2 font-semibold text-md text-gray-900 dark:text-white leading-tight"
          >
            デプロイ履歴
            <UBadge
              v-if="currentTotalCount > 0"
              :label="currentTotalCount"
            />
          </h2>

          <!-- Tab Navigation on the right -->
          <UTabs
            v-model="selectedHistoryTab"
            :items="tabItems"
            :ui="{
              wrapper: '',
              list: {
                height: 'h-9',
                tab: { height: 'h-7', size: 'text-[13px]' }
              }
            }"
          />
        </div>
      </template>

      <!-- Shared Filters -->
      <div class="flex items-center justify-between gap-3 px-4 py-3">
        <div>
          <BaseDateRangePicker
            v-if="selectedHistoryTab === 1"
            :model-value="range"
            @update:model-value="(newValue) => (range = newValue)"
          />
        </div>
        <UButton
          icon="prime:sync"
          color="gray"
          size="sm"
          @click="refresh"
        />
      </div>

      <!-- Content based on selected tab -->
      <DeploymentSwapHistoryTable
        v-if="selectedHistoryTab === 0"
        :rows="swapHistories"
        :loading="loadings.fetchSwapHistories"
      />

      <DeploymentSyncHistoryTable
        v-else-if="selectedHistoryTab === 1"
        :rows="deploymentHistories"
        :loading="loadings.fetchDeploymentHistories || loadings.fetchLabels"
        @view-logs="onViewLogs"
      />

      <!-- Shared Pagination -->
      <template #footer>
        <div class="flex flex-wrap justify-between items-center">
          <div class="flex items-center gap-1.5">
            <span class="text-sm leading-5">表示件数:</span>
            <USelect
              v-model="currentPagination.pageCount"
              :options="[3, 5, 10, 20, 30, 40]"
              class="w-20"
            />
          </div>

          <UPagination
            v-if="currentPagination.pageCount < currentPageTotal"
            v-model="currentPagination.page"
            :page-count="currentPagination.pageCount"
            :total="currentPageTotal"
            :ui="{
              wrapper: 'flex items-center gap-1',
              rounded: '!rounded-full min-w-[32px] justify-center',
              default: {
                activeButton: {
                  variant: 'outline'
                }
              }
            }"
          />
        </div>
      </template>
    </UCard>

    <DeploymentLogsModal
      v-model="isLogsModalOpen"
      :deploy-logs="deploymentLogs"
      :deploy-log-pagination="deploymentLogPagination"
      :loadings="loadings.getDeploymentLogs"
      :deploy-log-filter="deployLogFilter"
      :total-count="deployLogTotal"
      :on-fetch-logs="fetchDeploymentLogs"
    />
  </UDashboardPanelContent>
</template>
